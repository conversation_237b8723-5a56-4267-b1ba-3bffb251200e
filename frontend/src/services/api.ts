import axios from 'axios';
import { Api<PERSON><PERSON>po<PERSON>, FolderContent, SearchResult } from '../types';

// Dynamic API URL detection
const getApiBaseUrl = () => {
  // If environment variable is set, use it
  if (process.env.REACT_APP_API_URL) {
    return process.env.REACT_APP_API_URL;
  }

  // If accessing from localhost, use localhost
  if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {
    return 'http://localhost:3000';
  }

  // Otherwise, use the same host as frontend with port 3000
  return `http://${window.location.hostname}:3000`;
};

const API_BASE_URL = getApiBaseUrl();

const api = axios.create({
  baseURL: `${API_BASE_URL}/api/v1.0`,
  timeout: 30000,
});

// Request interceptor
api.interceptors.request.use(
  (config) => {
    // Add auth token if available
    const token = localStorage.getItem('token');
    if (token) {
      config.headers.token = token;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor
api.interceptors.response.use(
  (response) => {
    // Backend returns {code, data, message} structure
    return response.data;
  },
  (error) => {
    if (error.response?.status === 401) {
      // Handle unauthorized
      localStorage.removeItem('token');
      window.location.href = '/login';
    }
    return Promise.reject(error.response?.data || error.message);
  }
);

export const fileApi = {
  // Upload file
  uploadFile: (file: File, parentId?: string, onProgress?: (progress: number) => void): Promise<ApiResponse> => {
    const formData = new FormData();
    formData.append('file', file);
    if (parentId) {
      formData.append('parentId', parentId);
    }

    return api.post('/files/upload', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
      onUploadProgress: (progressEvent) => {
        if (onProgress && progressEvent.total) {
          const progress = Math.round((progressEvent.loaded * 100) / progressEvent.total);
          onProgress(progress);
        }
      },
    });
  },

  // Download file
  downloadFile: (fileId: string): string => {
    return `${API_BASE_URL}/api/v1.0/files/download/${fileId}`;
  },

  // Preview file
  previewFile: (fileId: string): string => {
    return `${API_BASE_URL}/api/v1.0/files/preview/${fileId}`;
  },

  // Browse folder content
  browseFolder: (folderId?: string): Promise<ApiResponse<FolderContent>> => {
    const url = folderId ? `/browse/${folderId}` : '/browse';
    return api.get(url);
  },

  // Create folder
  createFolder: (folderName: string, parentId?: string): Promise<ApiResponse> => {
    return api.post('/folders/create', {
      folderName,
      parentId: parentId || null,
    });
  },

  // Rename item
  renameItem: (itemId: string, newName: string): Promise<ApiResponse> => {
    return api.put(`/items/${itemId}/rename`, {
      name: newName,
    });
  },

  // Delete item
  deleteItem: (itemId: string): Promise<ApiResponse> => {
    return api.delete(`/items/${itemId}`);
  },

  // Search
  search: (query: string): Promise<ApiResponse<SearchResult>> => {
    return api.get('/search', {
      params: { q: query },
    });
  },
};

export default api;
