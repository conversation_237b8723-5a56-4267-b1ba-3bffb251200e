const CONSTANTS = require('../../../../const');
const MESSAGES = require('../../../../message');
const telegramService = require('../../../../services/telegram');
const cacheService = require('../../../../services/cache');
const fs = require('fs');
const path = require('path');

module.exports = async (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({
        code: CONSTANTS.CODE.INVALID_PARAMS,
        message: 'No file uploaded'
      });
    }

      const { originalname, filename, path: filePath, size, mimetype } = req.file;
      const parentId = req.body.parentId || null;

      // Validate parent folder if provided
      if (parentId) {
        const FolderModel = require('../../../../models/folder');
        const parentFolder = await FolderModel.findOne({
          _id: parentId,
          isDeleted: false
        });

        if (!parentFolder) {
          // Clean up uploaded file
          fs.unlinkSync(filePath);
          return next({
            code: CONSTANTS.CODE.INVALID_PARAMS,
            message: 'Parent folder not found'
          });
        }
      }

      // Upload to Telegram
      const telegramFileId = await telegramService.uploadFile(filePath, originalname);

      // Create file record in database
      const FileModel = require('../../../../models/file');
      const fileRecord = new FileModel({
        telegramFileId,
        originalFileName: originalname,
        fileSize: size,
        mimeType: mimetype,
        parentId: parentId || null,
        uploadDate: new Date()
      });

      await fileRecord.save();

      // Clean up local file
      fs.unlinkSync(filePath);

      // Clear cache for parent folder
      await cacheService.clearFolderCache(parentId);
      await cacheService.clearSearchCache();

      logger.logInfo(`File uploaded successfully: ${originalname}, ID: ${fileRecord._id}`);

    return res.json({
      code: CONSTANTS.CODE.SUCCESS,
      data: {
        id: fileRecord._id,
        telegramFileId: fileRecord.telegramFileId,
        originalFileName: fileRecord.originalFileName,
        fileSize: fileRecord.fileSize,
        mimeType: fileRecord.mimeType,
        uploadDate: fileRecord.uploadDate,
        parentId: fileRecord.parentId
      },
      message: 'File uploaded successfully'
    });

  } catch (error) {
    logger.logInfo(['files/upload error', error.message], __dirname);
    console.error('Upload error:', error);

    // Clean up local file if exists
    if (req.file && req.file.path && fs.existsSync(req.file.path)) {
      try {
        fs.unlinkSync(req.file.path);
      } catch (cleanupError) {
        logger.logInfo(['files/upload cleanup error', cleanupError.message], __dirname);
        console.error('Cleanup error:', cleanupError);
      }
    }

    return res.status(500).json({
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: error.message || MESSAGES.SYSTEM.ERROR
    });
  }
};
