const CONSTANTS = require('../../../const');
const MESSAGES = require('../../../message');
const cacheService = require('../../../services/cache');

module.exports = async (req, res) => {
  const validateParams = async (next) => {
    try {
      const query = req.query.q;

      if (!query || typeof query !== 'string' || query.trim().length === 0) {
        return next({
          code: CONSTANTS.CODE.INVALID_PARAMS,
          message: 'Search query is required'
        });
      }

      if (query.trim().length < 2) {
        return next({
          code: CONSTANTS.CODE.INVALID_PARAMS,
          message: 'Search query must be at least 2 characters long'
        });
      }

      next(null, query.trim());
    } catch (error) {
      logger.logError(['search/validateParams', error], __dirname);
      next({
        code: CONSTANTS.CODE.SYSTEM_ERROR,
        message: MESSAGES.SYSTEM.ERROR
      });
    }
  };

  const checkCache = async (query, next) => {
    try {
      const cacheKey = cacheService.getSearchKey(query);
      const cachedData = await cacheService.get(cacheKey);

      if (cachedData) {
        return next(null, null, cachedData); // Skip database query
      }

      next(null, query, null);
    } catch (error) {
      logger.logError(['search/checkCache', error], __dirname);
      // Continue without cache on error
      next(null, query, null);
    }
  };

  const searchItems = async (query, cachedData, next) => {
    try {
      if (cachedData) {
        return next(null, cachedData);
      }

      const FileModel = require('../../../models/file');
      const FolderModel = require('../../../models/folder');

      // Create case-insensitive regex for search
      const searchRegex = new RegExp(query.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'i');

      // Search files
      const files = await FileModel.find({
        originalFileName: searchRegex,
        isDeleted: false
      })
      .select('_id originalFileName fileSize mimeType uploadDate parentId telegramFileId')
      .sort({ originalFileName: 1 })
      .limit(100) // Limit results to prevent performance issues
      .lean();

      // Search folders
      const folders = await FolderModel.find({
        folderName: searchRegex,
        isDeleted: false
      })
      .select('_id folderName createdAt parentId')
      .sort({ folderName: 1 })
      .limit(100) // Limit results to prevent performance issues
      .lean();

      // Format response
      const result = {
        query: query,
        totalResults: files.length + folders.length,
        folders: folders.map(folder => ({
          id: folder._id,
          name: folder.folderName,
          type: 'folder',
          createdAt: folder.createdAt,
          parentId: folder.parentId
        })),
        files: files.map(file => ({
          id: file._id,
          name: file.originalFileName,
          type: 'file',
          size: file.fileSize,
          mimeType: file.mimeType,
          uploadDate: file.uploadDate,
          parentId: file.parentId,
          telegramFileId: file.telegramFileId
        }))
      };

      // Cache the result for 10 minutes
      const cacheKey = cacheService.getSearchKey(query);
      await cacheService.set(cacheKey, result, 600);

      next(null, result);
    } catch (error) {
      logger.logError(['search/searchItems', error], __dirname);
      next({
        code: CONSTANTS.CODE.SYSTEM_ERROR,
        message: MESSAGES.SYSTEM.ERROR
      });
    }
  };

  async.waterfall([validateParams, checkCache, searchItems], (err, data) => {
    if (err && _.isError(err)) {
      data = {
        code: CONSTANTS.CODE.SYSTEM_ERROR,
        message: MESSAGES.SYSTEM.ERROR
      };
    }

    const response = err || {
      code: CONSTANTS.CODE.SUCCESS,
      data: data,
      message: 'Search completed successfully'
    };

    res.json(response);
  });
};
